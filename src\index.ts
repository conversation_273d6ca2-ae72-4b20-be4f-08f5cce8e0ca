import { swagger } from "@elysiajs/swagger";
import { <PERSON>sia } from "elysia";
import mongoose from "mongoose";
import { env } from "./config/env";
import { closeDatabaseConnection, connectToDatabase } from "./config/mongodb";
import { redisConnection } from "./config/redisConnection";
import { authApp } from "./routes/auth";
import { jobRoutes } from "./routes/job.route";
import { jobsApp } from "./routes/jobs";

await connectToDatabase();
new Elysia()
	.onError(({ code, error, set }) => {
		console.error("Elysia error:", error);
		set.status = 500;
		return {
			status: "error",
			message: "message" in error ? error?.message : "Internal Server Error",
			code,
		};
	})
	.use(swagger())
	.get("/", async () => {
		return "Hello, Elysia! MongoDB is connected.";
	})
	.get("/health", async () => {
		const connectionState = mongoose.connection.readyState;
		const stateMap = {
			0: "disconnected",
			1: "connected",
			2: "connecting",
			3: "disconnecting",
		};

		return {
			status: "OK",
			version: "1.0.0",
			uptime: process.uptime(),
			mongodb: {
				status:
					connectionState === 1
						? "connected"
						: stateMap[connectionState as keyof typeof stateMap],
				dbName: mongoose.connection.db?.databaseName || env.DATABASE_NAME,
			},
		};
	})
	.use(jobRoutes)
	.use(authApp)
	.use(jobsApp)
	.listen(env.PORT, async () => {
		console.log(`Elysia server is running at http://localhost:${env.PORT}`);
	});

process.on("SIGINT", async () => {
	await closeDatabaseConnection();
	await redisConnection.quit();
	process.exit(0);
});
