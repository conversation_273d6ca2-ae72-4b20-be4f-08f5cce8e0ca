import { env } from "../config/env";
import Candidate from "../models/candidate.model";
import Job from "../models/job.model";
import { Recruiter } from "../models/recruiter.model";
import { plivoScreeningCallService } from "../services/plivoScreeningCall";
import { formatScreeningQuestions } from "../utils/formatScreeningQuestions";

interface ScreeningQuestion {
	id: string;
	question: string;
	options?: string[];
	correctAnswer?: string;
}

interface ScreeningParams {
	screeningQuestions: ScreeningQuestion[];
	communicationChannel: string[];
}

type ScreeningOutcome = "pass" | "fail";

/**
 * Performs the automated screening logic.
 * @param params The specific parameters for this agent.
 * @param jobId The ID of the job.
 * @param candidateId The ID of the candidate.
 * @returns An outcome: "pass" or "fail".
 */
export const runScreening = async (
	params: ScreeningParams,
	jobId: string,
	candidateId: string,
): Promise<ScreeningOutcome> => {
	try {
		const webhook_endpoint = env.API_URL.replace("https://", "").replace(
			"http://",
			"",
		);

		// Fetch candidate details with null check
		const candidateDetails = await Candidate.findById(candidateId);
		if (!candidateDetails) {
			console.error(`Candidate not found with ID: ${candidateId}`);
			return "fail";
		}

		// Fetch job details with null check
		const jobDetails = await Job.findById(jobId);
		if (!jobDetails) {
			console.error(`Job not found with ID: ${jobId}`);
			return "fail";
		}

		// Fetch recruiter organization with population
		const recruiterOrg = await Recruiter.findById(
			jobDetails.createdBy,
		).populate("organization");

		if (!recruiterOrg || !recruiterOrg.organization) {
			console.error(`Recruiter or organization not found for job: ${jobId}`);
			return "fail";
		}

		// Validate candidate phone number
		if (!candidateDetails.phone) {
			console.error(`Candidate phone number not found for ID: ${candidateId}`);
			return "fail";
		}

		const formattedTo = plivoScreeningCallService.formatPhoneNumber(
			candidateDetails.phone,
		);

		// Convert questions array to formatted string for Plivo API
		const questionsString = formatScreeningQuestions(params.screeningQuestions);

		// Access organization name safely with proper typing
		const organizationName =
			(recruiterOrg.organization as unknown as { name?: string })?.name ||
			"Unknown Company";

		const payload = {
			questions: questionsString,
			companyName: organizationName,
			role: jobDetails.title,
			to: formattedTo,
			webhook_endpoint,
		};

		console.log("Initiating screening call with payload:", payload);

		const response =
			await plivoScreeningCallService.initiateScreeningCall(payload);

		return response.success ? "pass" : "fail";
	} catch (error) {
		console.error("Error in runScreening:", error);
		return "fail";
	}
};
