// src/workers/workflow.processor.ts
import type { Job } from "bullmq";
import { AgentTask, TaskStatus } from "../models/agentTask.model";
import Candidate from "../models/candidate.model";
import { JobConfigModel } from "../models/jobConfig.model";
import { dispatchAgentTask } from "../services/agent.service";

// This is the heart of our worker. It processes one job from the queue.
export const processStageJob = async (job: Job) => {
	const { jobId, candidateId, currentStage } = job.data;
	console.log(
		`Processing job ${job.id} for candidate ${candidateId} at stage '${currentStage}'`,
	);

	// 1. Fetch the full JobConfig to find the instructions for this stage
	const jobConfig = await JobConfigModel.findOne({ jobId }).lean();
	if (!jobConfig) {
		throw new Error(`JobConfig not found for jobId: ${jobId}`);
	}

	const stageConfig = jobConfig.stageConfig.find(
		(sc) => sc.stage === currentStage,
	);
	if (!stageConfig) {
		throw new Error(`Configuration for stage '${currentStage}' not found.`);
	}

	// 2. Create an AgentTask to log that we are starting the work
	const agentTask = await AgentTask.create({
		jobId,
		candidateId,
		agentId: stageConfig.action.agentId,
		stage: currentStage,
		status: "in_progress", // Or use the initial "pending"
	});

	await Candidate.findByIdAndUpdate(candidateId, { stage: currentStage });

	try {
		const outcome = await dispatchAgentTask(
			stageConfig.action.agentId,
			stageConfig.action.params,
			stageConfig.action.outputs,
			jobId,
			candidateId,
		);

		// 4. On success, update the AgentTask with the result
		agentTask.status = TaskStatus.COMPLETED;
		agentTask.result = { outcome };
		await agentTask.save();

		// Return the outcome so the next step knows where to send the candidate
		return { outcome };
	} catch (error) {
		// 5. On failure, update the AgentTask with the error
		const reason =
			error instanceof Error ? error.message : "An unknown error occurred.";
		console.error(`Job ${job.id} failed: ${reason}`);
		agentTask.status = TaskStatus.FAILED;
		agentTask.result = { error: reason };
		await agentTask.save();

		// Re-throw the error to let BullMQ handle the job failure (e.g., retries)
		throw error;
	}
};
