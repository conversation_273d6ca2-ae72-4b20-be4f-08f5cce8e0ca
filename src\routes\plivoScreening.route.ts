import { Elysia, t } from "elysia";
import { env } from "../config/env";
import { plivoScreeningCallService } from "../services/plivoScreeningCall";

export const vedaScreeningRoute = new Elysia({
	prefix: "/veda-screening",
})
	.post(
		"/update-status",
		async ({ body }) => {
			const { phoneNumber, status, screeningCleared } = body;

			console.log(phoneNumber, status, screeningCleared);

			return {
				status: true,
				message: "Status updated successfully",
			};
		},
		{
			body: t.Object({
				phoneNumber: t.String(),
				status: t.String(),
				screeningCleared: t.Optional(t.String()),
			}),
		},
	)
	.post(
		"/trigger-screening-call",
		async ({ body }) => {
			const { questions, companyName, role, to } = body;

			const webhook_endpoint = env.API_URL.replace("https://", "").replace(
				"http://",
				"",
			);

			const formattedTo = plivoScreeningCallService.formatPhoneNumber(to);

			const payload = {
				questions,
				companyName,
				role,
				to: formattedTo,
				webhook_endpoint,
			};

			console.log(payload);

			const response =
				await plivoScreeningCallService.initiateScreeningCall(payload);

			return {
				status: response.success,
				message: response.message,
			};
		},
		{
			body: t.Object({
				questions: t.String(),
				companyName: t.String(),
				role: t.String(),
				to: t.String(),
			}),
		},
	);
