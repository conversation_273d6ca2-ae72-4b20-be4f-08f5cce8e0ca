// src/services/workflow.service.ts

import { NotFoundError } from "elysia";
import Candidate from "../models/candidate.model"; // Adjust path as needed
import { JobConfigModel } from "../models/jobConfig.model"; // Adjust path as needed
import { getQueue } from "./queue.service";

/**
 * Starts the workflow for a given hiring job.
 * Fetches all candidates for the job and adds them to the first stage's queue.
 * @param jobId - The ID of the hiring job to start.
 */
export const startJobWorkflow = async (jobId: string) => {
	// 1. Fetch the job configuration from the database
	const jobConfig = await JobConfigModel.findOne({ jobId }).lean();
	if (!jobConfig) {
		throw new NotFoundError(`JobConfig not found for jobId: ${jobId}`);
	}

	// 2. Identify the first stage from the flow
	const firstStage = jobConfig.flow[0]?.stage;
	if (!firstStage) {
		throw new Error(`Workflow for job ${jobId} has no starting stage.`);
	}

	// 3. Fetch all candidates associated with this job
	const candidates = await Candidate.find({ jobId }).select("_id").lean();
	if (candidates.length === 0) {
		console.log(`No candidates found for job ${jobId}. Workflow not started.`);
		return { message: "No candidates to process." };
	}

	// 4. Get the queue for the first stage
	const firstStageQueue = getQueue(firstStage);

	// 5. Add each candidate to the queue as a separate job
	const jobs = [];
	for (const candidate of candidates) {
		const jobData = {
			jobId: jobConfig.jobId,
			candidateId: candidate._id,
			currentStage: firstStage, // Pass the current stage name
		};

		// The job name can be descriptive for easier debugging/logging
		const jobName = `process-candidate-${candidate._id}-stage-${firstStage}-job-${jobId}`;

		jobs.push(firstStageQueue.add(jobName, jobData));
	}

	await Promise.all(jobs);

	return {
		message: `Successfully enqueued ${candidates.length} candidates for stage '${firstStage}'.`,
	};
};
