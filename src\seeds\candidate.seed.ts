import { connectToDatabase } from "../config/mongodb";
import Candidate, { StageStatus } from "../models/candidate.model";
import Job from "../models/job.model";
import { generateRandomString } from "../services/mockExternalATS.service";

await connectToDatabase();

export async function seedCandidates() {
	const firstJob = await Job.findOne({});

	if (!firstJob) {
		console.error("No job found");
		return;
	}

	const candidateCount = await Candidate.countDocuments();
	if (candidateCount === 0) {
		console.log("Seeding dummy candidate data...");
		const dummyCandidates = [
			{
				name: "<PERSON>",
				jobId: firstJob._id,
				email: "<EMAIL>",
				phone: "+11234567890",
				stage: "applied",
				resumeLink:
					"https://placed-global-data.s3.ap-south-1.amazonaws.com/resumes/b4e1a62e-44f9-4479-9a36-53b25c9d1875_1745564813916.pdf",
				expectedSalary: 70000,
				contactInfo: {
					email: "<EMAIL>",
					phone: "+11234567890",
					linkedin: "linkedin.com/in/alice",
				},
				status: StageStatus.PENDING,
				source: "manual",
				sourceUid: generateRandomString(10),
			},
			{
				name: "Bob Williams",
				jobId: firstJob._id,
				email: "<EMAIL>",
				phone: "+19876543210",
				resumeLink:
					"https://placed-global-data.s3.ap-south-1.amazonaws.com/resumes/27a7be86-320a-4cbe-9976-2af2cc5db316_1750129677129.pdf",
				expectedSalary: 85000,
				contactInfo: {
					email: "<EMAIL>",
					phone: "+19876543210",
					github: "github.com/bobw",
				},
				stage: "applied",
				status: StageStatus.PENDING,
				source: "manual",
				sourceUid: generateRandomString(10),
			},
			{
				name: "Charlie Brown",
				jobId: firstJob._id,
				email: "<EMAIL>",
				phone: "+15551234567",
				sumeLink:
					"https://placed-global-data.s3.ap-south-1.amazonaws.com/resumes/46fced72-8ec3-4d3f-a5ba-ec03b4776b1f_1745260411542.pdf",
				expectedSalary: 60000,
				contactInfo: {
					email: "<EMAIL>",
					phone: "+15551234567",
				},
				status: StageStatus.PENDING,
				stage: "applied",
				source: "manual",
				sourceUid: generateRandomString(10),
			},
			{
				name: "Diana Prince",
				jobId: firstJob._id,
				email: "<EMAIL>",
				phone: "+12223334444",
				resumeLink:
					"https://placed-global-data.s3.ap-south-1.amazonaws.com/resumes/1733f071-17e9-4dc1-a42f-49edafa6993a_1745343921812.pdf",
				currentStage: "applied",
				expectedSalary: 95000,
				contactInfo: {
					email: "<EMAIL>",
					phone: "+12223334444",
					linkedin: "linkedin.com/in/diana",
				},
				status: StageStatus.PENDING,
				stage: "applied",
				source: "manual",
				sourceUid: generateRandomString(10),
			},
		];

		try {
			await Candidate.insertMany(dummyCandidates);
			console.log("Dummy candidates seeded successfully!");
		} catch (error) {
			console.error("Error seeding candidates:", error);
		}
	}
}

seedCandidates();
