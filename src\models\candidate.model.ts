import mongoose, { type Document, Schema, type Types } from "mongoose";

// Enum for candidate status
export enum StageStatus {
	PENDING = "pending",
	COMPLETED = "completed",
	FAILED = "failed",
	REJECTED = "rejected",
}

// Interface for candidate contact information
export interface ContactInfo {
	email: string;
	phone?: string;
	linkedin?: string;
	github?: string;
	address?: string;
}

export interface ICandidate extends Document {
	/**
	 * Unique identifier for the candidate on the source platform
	 */
	sourceUid: string;
	name: string;
	email: string;
	phone: string;
	resumeLink: string;
	jobId: Types.ObjectId;
	stage: string;
	expectedSalary?: number;
	contactInfo: ContactInfo;
	status: string;
	source: string; // e.g., 'job_board', 'referral', 'linkedin', 'company_website'
	createdAt: Date;
	updatedAt: Date;
	createdBy?: string; // User ID who created the record
}

// Mongoose Schema
const candidateSchema = new Schema<ICandidate>(
	{
		sourceUid: {
			type: String,
		},
		name: {
			type: String,
			required: true,
			trim: true,
			maxlength: 100,
		},
		jobId: {
			type: Schema.Types.ObjectId,
			ref: "Job",
			required: true,
		},
		email: {
			type: String,
			required: true,
			lowercase: true,
			trim: true,
			match: [
				/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/,
				"Please enter a valid email",
			],
		},
		phone: {
			type: String,
			trim: true,
			match: [/^[+]?[1-9][\d]{0,15}$/, "Please enter a valid phone number"],
		},
		resumeLink: {
			type: String,
			required: true,
			trim: true,
		},
		stage: { type: String, required: true, default: "applied" }, // initial
		source: {
			type: String,
			required: true,
		},
		expectedSalary: {
			type: Number,
			min: 0,
		},
		contactInfo: {
			email: {
				type: String,
				required: true,
				lowercase: true,
				trim: true,
			},
			phone: String,
			linkedin: String,
			github: String,
			address: String,
		},
		// Status and Metadata
		status: {
			type: String,
			enum: Object.values(StageStatus),
			default: StageStatus.PENDING,
			required: true,
		},
	},
	{
		timestamps: true,
	},
);
const Candidate = mongoose.model<ICandidate>("Candidate", candidateSchema);

export default Candidate;
