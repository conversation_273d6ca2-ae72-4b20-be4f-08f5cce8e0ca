// src/run-workers.ts (Corrected)
import { Worker } from "bullmq";
// Import your database connection functions
import { closeDatabaseConnection, connectToDatabase } from "./config/mongodb";
import { redisConnection } from "./config/redisConnection";
import { JobConfigModel } from "./models/jobConfig.model";
import { handleJobCompletion } from "./workers/workflow.events";
import { processStageJob } from "./workers/workflow.processor";

const BATCH_SIZE = 10;

async function startWorkers() {
	console.log("🚀 Starting workers...");

	// 1. ESTABLISH DATABASE CONNECTION FOR THIS PROCESS
	await connectToDatabase();
	console.log("✅ Worker process connected to MongoDB.");

	const allConfigs = await JobConfigModel.find({}).select("flow.stage").lean();
	const allStages = allConfigs.flatMap((config) =>
		config.flow.map((f) => f.stage),
	);
	const uniqueStages = [...new Set(allStages)].filter(
		(stage) => stage !== "stop" && stage !== "rejected",
	);

	if (uniqueStages.length === 0) {
		console.warn("No stages found in database. No workers will be started.");
		// Close connection if no workers are started
		await closeDatabaseConnection();
		return;
	}

	console.log(
		`Found stages: [${uniqueStages.join(", ")}]. Initializing a worker for each.`,
	);

	for (const stageName of uniqueStages) {
		const worker = new Worker(stageName, processStageJob, {
			connection: redisConnection,
			concurrency: BATCH_SIZE,
		});
		worker.on("completed", handleJobCompletion);
		worker.on("failed", (job, error) => {
			console.error(
				`Job ${job?.id} in stage ${job?.data.currentStage} failed with error: ${error.message}`,
			);
		});
		worker.on("error", (error) => {
			console.error(`Worker error in stage ${stageName}:`, error);
		});
	}

	console.log("✅ All workers are up and running and listening for jobs.");
}

startWorkers().catch((err) => {
	console.error("Failed to start workers:", err);
	process.exit(1);
});

// 2. ADD GRACEFUL SHUTDOWN FOR THIS PROCESS
process.on("SIGINT", async () => {
	console.log("\nReceived SIGINT. Shutting down workers gracefully...");
	await closeDatabaseConnection();
	await redisConnection.quit();
	console.log("Connections closed. Exiting.");
	process.exit(0);
});
