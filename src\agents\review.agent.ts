import axios from "axios";
import { extractText, getDocumentProxy } from "unpdf";
import { openaiClient } from "../config/openai";
import Candidate from "../models/candidate.model";
import Job from "../models/job.model";

interface ReviewParams {
	reviewCriteria: string;
	minimumScore: number;
}

/**
 * Performs an AI-driven review of a candidate's resume against a job description
 * using a dynamic set of possible outcomes.
 *
 * @param params Review parameters including review criteria and minimum score.
 * @param outputs A string array of possible outcomes (e.g., ["best", "good", "bad"]). The AI will choose one.
 * @param jobId The ID of the job being processed.
 * @param candidateId The ID of the candidate being reviewed.
 * @returns A promise that resolves to one of the outcome strings from the `outputs` array.
 */
export const runVedaReview = async (
	params: ReviewParams,
	outputs: string[],
	jobId: string,
	candidateId: string,
): Promise<string> => {
	console.log("Starting Veda Review...", params, outputs, jobId, candidateId);
	// A sensible fallback is the last item in the list, assuming it's the "worst" outcome.
	const fallbackOutcome = outputs[outputs.length - 1] || "error";

	try {
		// Validate that the outputs array is provided and not empty
		if (!outputs || outputs.length === 0) {
			throw new Error("The 'outputs' array for Veda review cannot be empty.");
		}

		console.log(
			`🤖 Starting Veda Review for candidate ${candidateId} and job ${jobId}.`,
		);
		console.log(`⚖️ Using custom outcomes: [${outputs.join(", ")}]`);

		// 1. Fetch Job and Candidate data concurrently
		const [job, candidate] = await Promise.all([
			Job.findById(jobId).lean(),
			Candidate.findById(candidateId).lean(),
		]);

		// Validate that the records and resume link exist
		if (!job) throw new Error(`Job with ID ${jobId} not found.`);
		if (!candidate)
			throw new Error(`Candidate with ID ${candidateId} not found.`);
		if (!candidate.resumeLink) {
			console.warn(`Candidate ${candidateId} has no resume link.`);
			return fallbackOutcome; // Cannot proceed without a resume
		}

		// 2. Download and parse the resume PDF
		console.log(`📄 Parsing resume from: ${candidate.resumeLink}`);
		const pdfBuffer = await axios.get(candidate.resumeLink, {
			responseType: "arraybuffer",
		});
		const bytes = new Uint8Array(pdfBuffer.data); // axios returns ArrayBuffer -> wrap in Uint8Array
		const pdf = await getDocumentProxy(bytes);
		const { text } = await extractText(pdf, { mergePages: true });

		const resumeText = text;

		if (!resumeText) {
			console.warn(
				`Could not extract text from resume for candidate ${candidateId}.`,
			);
			return fallbackOutcome; // Consider a bad match if resume is unreadable
		}

		// 3. Construct a DYNAMIC prompt that incorporates the `outputs` array
		const systemMessage = `
            You are an expert AI Technical Recruiter. Your task is to analyze a candidate's resume against a job description and classify their overall fit.

            Based on your complete analysis, you must choose the SINGLE most appropriate category from the following list I provide:
            ${outputs.map((o) => `- "${o}"`).join("\n            ")}

            Provide your response as a JSON object with a single key "outcome". The value for this key MUST BE one of the exact strings from the list I provided. Do not add any other text or explanation.
            Here is an important review criteria to keep in mind: ${params.reviewCriteria}

            For example, if the provided list is ["Qualified", "Not Qualified"], a valid response would be: {"outcome": "Qualified"}
        `;

		const userPrompt = `
            Please evaluate the following candidate's resume for the given job opening.

            **JOB DETAILS:**
            - **Title:** ${job.title}
            - **Experience Level:** ${job.experienceLevel}
            - **Description:** ${job.description}
            - **Key Responsibilities:**
                - ${job.responsibilities?.join("\n                - ")}
            - **Required Skills:**
                - ${job.requiredSkills?.join("\n                - ")}

            ---

            **CANDIDATE'S RESUME:**
            ${resumeText}
        `;

		// 4. Call the OpenAI API with the new dynamic prompt
		console.log("🧠 Feeding details to OpenAI for dynamic analysis...");
		const completion = await openaiClient.chat.completions.create({
			model: "gpt-4o",
			messages: [
				{ role: "system", content: systemMessage },
				{ role: "user", content: userPrompt },
			],
			response_format: { type: "json_object" },
			temperature: 0.2,
		});

		const result = JSON.parse(completion?.choices[0]?.message.content || "{}");
		const outcome = result.outcome;

		// 5. Validate the outcome against the DYNAMIC list of outputs
		if (!outputs.includes(outcome)) {
			throw new Error(
				`Received invalid outcome from OpenAI: "${outcome}". It was not in the expected list: [${outputs.join(", ")}]`,
			);
		}

		console.log(`✅ Veda Review complete. Dynamic Outcome: ${outcome}`);
		return outcome;
	} catch (error) {
		console.error("❌ Error during Veda Review:", error);
		// Return the fallback outcome on any failure
		return fallbackOutcome;
	}
};
