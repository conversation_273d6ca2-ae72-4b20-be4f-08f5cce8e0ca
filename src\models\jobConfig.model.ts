import mongoose, { type Document, Schema, type Types } from "mongoose";

// Mongoose Document interfaces
export interface INextStatusConfig {
	stage: string;
	outcome: string;
}

export interface IStatusConfig {
	stage: string;
	next: INextStatusConfig[];
}

export interface IStageAction {
	agentId: string;
	outputs: string[];
	statuses?: string[];
	params: Record<string, unknown>;
}

export interface IStageConfig {
	stage: string;
	action: IStageAction;
}

export interface IJobConfig extends Document {
	jobId: Types.ObjectId;
	flow: IStatusConfig[];
	stageConfig: IStageConfig[];
}

// Mongoose Schemas
const NextStatusConfigSchema = new Schema<INextStatusConfig>({
	stage: { type: String, required: true },
	outcome: { type: String, required: true },
});

const StatusConfigSchema = new Schema<IStatusConfig>({
	stage: { type: String, required: true },
	next: [NextStatusConfigSchema],
});

const StageActionSchema = new Schema<IStageAction>({
	agentId: { type: String, required: true },
	outputs: [{ type: String, required: true }],
	statuses: [{ type: String }],
	params: { type: Schema.Types.Mixed, required: true },
});

const StageConfigSchema = new Schema<IStageConfig>({
	stage: { type: String, required: true },
	action: { type: StageActionSchema, required: true },
});

const JobConfigSchema = new Schema<IJobConfig>(
	{
		jobId: { type: Schema.Types.ObjectId, ref: "Job", required: true },
		flow: [StatusConfigSchema],
		stageConfig: [StageConfigSchema],
	},
	{
		timestamps: true,
	},
);

// Create and export the model
export const JobConfigModel = mongoose.model<IJobConfig>(
	"JobConfig",
	JobConfigSchema,
);
