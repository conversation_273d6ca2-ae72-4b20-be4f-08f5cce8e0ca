// src/services/agent.service.ts (Updated)
import { runVedaReview } from "../agents/review.agent";
import { runScreening } from "../agents/screening.agent";

/**
 * Acts as a router to call the correct agent logic based on the agentId.
 * @param agentId The ID of the agent to execute (e.g., "reviewAgent").
 * @param params The parameters for that agent's task.
 * @param candidateId The ID of the candidate being processed.
 * @param jobId The ID of the job being processed.
 * @returns A promise that resolves with the agent's outcome.
 */
export const dispatchAgentTask = (
	agentId: string,
	params: any,
	outputs: string[],
	jobId: string,
	candidateId: string,
): Promise<string> => {
	switch (agentId) {
		case "reviewAgent":
			return runVedaReview(params, outputs, jobId, candidateId);

		case "screeningAgent":
			return runScreening(params, jobId, candidateId);

		default:
			console.error(`Unknown agentId: ${agentId}`);
			throw new Error(`No agent implementation found for agentId: ${agentId}`);
	}
};
