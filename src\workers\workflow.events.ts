// src/workers/workflow.events.ts
import type { Job } from "bullmq";
import Candidate, { StageStatus } from "../models/candidate.model";
import { JobConfigModel } from "../models/jobConfig.model";
import { getQueue } from "../services/queue.service";

/**
 * Handles the logic after a job is successfully completed.
 * It finds the next stage based on the job's outcome and enqueues the candidate.
 * @param job The completed job object.
 * @param result The return value from the job's processor function.
 */
export const handleJobCompletion = async (
	job: Job,
	result: { outcome: string },
) => {
	const { jobId, candidateId, currentStage } = job.data;
	const { outcome } = result;

	console.log(
		`Job ${job.id} completed. Candidate ${candidateId} at stage '${currentStage}' had outcome: '${outcome}'.`,
	);

	// 1. Fetch the JobConfig to find the workflow rules
	const jobConfig = await JobConfigModel.findOne({ jobId }).lean();
	if (!jobConfig) {
		console.error(`Could not find JobConfig for ${jobId} to advance workflow.`);
		return;
	}

	// 2. Find the current stage in the flow to see where to go next
	const currentFlow = jobConfig.flow.find((f) => f.stage === currentStage);
	if (!currentFlow) {
		console.error(`Could not find flow for stage '${currentStage}'.`);
		return;
	}

	// 3. Find the next step that matches the outcome
	const nextStep = currentFlow.next.find((n) => n.outcome === outcome);
	if (!nextStep) {
		console.log(
			`No next stage defined for outcome '${outcome}' at stage '${currentStage}'. Workflow for candidate ends here.`,
		);
		return;
	}

	const nextStage = nextStep.stage;

	// 4. Check for the "stop" condition to end the workflow
	if (nextStage === "stop" || nextStage === "rejected") {
		console.log(
			`🏁 Workflow for candidate ${candidateId} has reached the terminal stage: '${nextStage}'.`,
		);
		// Here you could update the candidate's master status in the DB, e.g.,
		if (nextStage === "rejected") {
			await Candidate.updateOne(
				{ _id: candidateId },
				{ status: StageStatus.REJECTED },
			);
		}

		if (nextStage === "stop") {
			await Candidate.updateOne(
				{ _id: candidateId },
				{ status: StageStatus.COMPLETED },
			);
		}
		return;
	}

	// 5. Add the candidate to the queue for the next stage
	console.log(`➡️  Advancing candidate ${candidateId} to stage: '${nextStage}'`);
	const nextStageQueue = getQueue(nextStage);
	const nextJobData = { ...job.data, currentStage: nextStage };

	await nextStageQueue.add(
		`process-candidate-${candidateId}-stage-${nextStage}`,
		nextJobData,
	);
};
